<!-- WhatsApp Assistant - Layout Chat SDR Premium -->
<div class="whatsapp-assistant">

  <!-- Loading -->
  <div *ngIf="carregandoDados" class="loading-screen">
    <div class="spinner-border" role="status">
      <span class="sr-only">Carregando...</span>
    </div>
    <p>Carregando dados do contato...</p>
  </div>

  <!-- Layout Principal -->
  <div *ngIf="!carregandoDados" class="chat-layout">

    <!-- Header do Chat -->
    <div class="chat-header">
      <div class="assistant-info">
        <div class="assistant-avatar">
          <i class="fe-zap"></i>
        </div>
        <div class="assistant-details">
          <h1 class="assistant-name">Assistente SDR Premium</h1>
          <p class="assistant-subtitle">
            <span class="status-dot"></span>
            Especialista Meu Cardápio • IA Avançada
          </p>
        </div>
      </div>
      <div class="header-actions">
        <button class="header-btn">
          <i class="fe-settings"></i>
        </button>
        <button class="header-btn">
          <i class="fe-message-circle"></i>
        </button>
      </div>
    </div>

    <!-- Card Principal do Lead -->
    <div class="lead-card">
      <div class="lead-content">
        <div class="lead-avatar">
          <div class="avatar-circle">
            {{ getInitials(contato.nomeResponsavel || contato.empresa || nomeWhatsApp) }}
          </div>
        </div>
        <div class="lead-info">
          <h1 class="lead-name">{{ contato.nomeResponsavel || contato.empresa || nomeWhatsApp || 'NOVO CONTATO' }}</h1>
          <div class="lead-status">
            <span class="status-badge" [class.registered]="contato.id">
              {{ contato.id ? 'Lead Cadastrado' : 'Novo Lead' }}
            </span>
          </div>
        </div>
        <div class="lead-score" *ngIf="contato.score">
          <span class="score-badge">{{ contato.score }}% Score</span>
        </div>
      </div>
    </div>

    <!-- Alerta de Lead Mal Cadastrado -->
    <div class="alert-bad-lead" *ngIf="isLeadMalCadastrado()">
      <div class="alert-content">
        <div class="alert-icon">
          <i class="fe-alert-triangle"></i>
        </div>
        <div class="alert-text">
          <h3>⚠️ Lead mal cadastrado!</h3>
          <p>O nome do responsável está genérico ({{ contato.nomeResponsavel }}).
             <strong>Atualize com o nome real da pessoa</strong> para melhorar a personalização das mensagens.</p>
        </div>
        <div class="alert-action">
          <button class="fix-btn" (click)="abrirEdicaoLead()">
            <i class="fe-edit-2"></i>
            Corrigir
          </button>
        </div>
      </div>
    </div>

    <!-- Card Detalhes do Lead -->
    <div class="detail-card">
      <div class="card-header">
        <i class="fe-user"></i>
        <span>Detalhes</span>
      </div>
      <div class="detail-grid">
        <div class="detail-item">
          <div class="detail-icon empresa">
            <i class="fe-briefcase"></i>
          </div>
          <div class="detail-content">
            <div class="detail-label">EMPRESA</div>
            <div class="detail-value">{{ contato.crmEmpresa?.nome || contato.empresa || 'GAMAN' }}</div>
          </div>
        </div>

        <div class="detail-item">
          <div class="detail-icon telefone">
            <i class="fe-phone"></i>
          </div>
          <div class="detail-content">
            <div class="detail-label">TELEFONE</div>
            <div class="detail-value">{{ contato.telefone || '556234348879' }}</div>
          </div>
        </div>

        <div class="detail-item">
          <div class="detail-icon segmento">
            <i class="fe-tag"></i>
          </div>
          <div class="detail-content">
            <div class="detail-label">SEGMENTO</div>
            <div class="detail-value">{{ contato.segmento || 'Restaurante' }}</div>
          </div>
        </div>

        <div class="detail-item" *ngIf="contato.instagramHandle">
          <div class="detail-icon instagram">
            <i class="fe-instagram"></i>
          </div>
          <div class="detail-content">
            <div class="detail-label">INSTAGRAM</div>
            <div class="detail-value">{{ contato.instagramHandle || '@gamanjapanesefood' }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Separador entre dados da empresa e chat -->
    <div class="section-separator">
      <div class="separator-line"></div>
      <div class="separator-text">
        <i class="fe-message-circle"></i>
        <span>Assistente de Vendas</span>
      </div>
      <div class="separator-line"></div>
    </div>

    <!-- Área de Chat -->
    <div class="chat-area">

      <!-- Mensagem Inicial do Assistente -->
      <div class="chat-message assistant-message">
        <div class="message-avatar">
          <i class="fe-cpu"></i>
        </div>
        <div class="message-content">
          <div class="message-text">
            <p><strong>👋 Olá! Sou seu assistente de vendas especializado no Meu Cardápio.</strong></p>
            <p>Estou aqui para transformar suas conversas em vendas com:</p>
            <ul>
              <li>📊 <strong>Mensagens de rapport que conectam</strong></li>
              <li>🎯 <strong>Perguntas SPIN selling certeiras</strong></li>
              <li>📞 <strong>Scripts de follow-up irresistíveis</strong></li>
              <li>🛡️ <strong>Objeções e respostas vencedoras</strong></li>
              <li>📱 <strong>Análise de conversas do WhatsApp</strong></li>
            </ul>
            <p><strong>Vamos começar? Escolha uma sugestão ou me conte sobre seu prospect!</strong></p>
          </div>
          <div class="message-time">21:17</div>
        </div>
      </div>

      <!-- Sugestões Inteligentes Unificadas -->
      <div class="smart-suggestions" *ngIf="!carregando">
        <div class="suggestions-header">
          <i class="fe-zap"></i>
          <span>Sugestões inteligentes:</span>
        </div>

        <div class="suggestions-grid">
          <button class="suggestion-card" (click)="gerarRapport()">
            <i class="fe-heart"></i>
            <span>Rapport pizzaria</span>
          </button>

          <button class="suggestion-card" (click)="gerarSugestaoEspecifica('spin-situacao')">
            <i class="fe-bar-chart-2"></i>
            <span>SPIN situação</span>
          </button>

          <button class="suggestion-card" (click)="gerarSugestaoEspecifica('objecao-dinheiro')">
            <i class="fe-dollar-sign"></i>
            <span>Objeção dinheiro</span>
          </button>

          <button class="suggestion-card" (click)="gerarSugestaoEspecifica('agendar-demo')">
            <i class="fe-calendar"></i>
            <span>Agendar demo</span>
          </button>

          <button class="suggestion-card" (click)="gerarSugestaoEspecifica('follow-up')">
            <i class="fe-phone-call"></i>
            <span>Follow-up</span>
          </button>

          <button class="suggestion-card" (click)="gerarSugestaoEspecifica('vamos-pensar')">
            <i class="fe-clock"></i>
            <span>Vamos pensar</span>
          </button>

          <button class="suggestion-card" (click)="gerarSugestaoEspecifica('lanchonete')">
            <i class="fe-coffee"></i>
            <span>Lanchonete</span>
          </button>

          <button class="suggestion-card" (click)="gerarSugestaoEspecifica('bar-boteco')">
            <i class="fe-beer"></i>
            <span>Bar/boteco</span>
          </button>
        </div>

        <!-- Botões de Categoria -->
        <div class="category-buttons">
          <button class="category-btn" [class.active]="categoriaAtiva === 'rapport'" (click)="selecionarCategoria('rapport')">
            <i class="fe-heart"></i>
            Rapport
          </button>
          <button class="category-btn" [class.active]="categoriaAtiva === 'spin'" (click)="selecionarCategoria('spin')">
            <i class="fe-target"></i>
            SPIN
          </button>
          <button class="category-btn" [class.active]="categoriaAtiva === 'whatsapp'" (click)="selecionarCategoria('whatsapp')">
            <i class="fe-message-square"></i>
            WhatsApp
          </button>
          <button class="category-btn" [class.active]="categoriaAtiva === 'objecao'" (click)="selecionarCategoria('objecao')">
            <i class="fe-shield"></i>
            Objeção
          </button>
        </div>
      </div>

      <!-- Mensagens de Sugestões (quando disponíveis) -->
      <div class="chat-message assistant-message" *ngFor="let sugestao of sugestoes; let i = index">
        <div class="message-avatar">
          <i class="fe-zap"></i>
        </div>
        <div class="message-content">
          <div class="message-text">
            {{ sugestao.texto }}
          </div>
          <div class="message-actions">
            <button class="action-btn copy" (click)="copiarSugestao(i)">
              <i class="fe-copy"></i>
              Copiar
            </button>
          </div>
        </div>
      </div>

    </div>

  </div>

  <!-- Área de Input do Chat (Fixa na parte inferior) -->
  <div class="chat-input-area">
    <div class="input-container">
      <input
        type="text"
        class="chat-input"
        placeholder="Digite sua mensagem..."
        [(ngModel)]="mensagemUsuario"
        (keyup.enter)="enviarMensagem()"
        [disabled]="carregando">
      <button
        class="send-btn"
        (click)="enviarMensagem()"
        [disabled]="carregando || !mensagemUsuario?.trim()">
        <i class="fe-send" *ngIf="!carregando"></i>
        <i class="fe-loader rotating" *ngIf="carregando"></i>
      </button>
    </div>
    <div class="input-hint">
      Pressione Enter para enviar • Shift+Enter para quebrar linha
    </div>
  </div>

</div>

