// WhatsApp Assistant - Layout Chat SDR Premium

// ===== RESET E VARIÁVEIS =====
* {
  box-sizing: border-box;
}

.whatsapp-assistant {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  background: #f0f2f5;
  min-height: 100vh;
  margin: 0;
  padding: 0;
  position: relative;
  overflow-x: hidden;
}

.chat-layout {
  width: 100%;
  max-width: 100%;
  margin: 0;
  background: #f0f2f5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

// ===== LOADING =====
.loading-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  gap: 1rem;

  .spinner-border {
    width: 2rem;
    height: 2rem;
    border-width: 3px;
    color: #25D366;
  }

  p {
    color: #667781;
    margin: 0;
  }
}

// ===== HEADER DO CHAT =====
.chat-header {
  background: white;
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.assistant-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.assistant-avatar {
  width: 40px;
  height: 40px;
  background: #1f2937;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;

  i {
    font-size: 1.25rem;
  }
}

.assistant-details {
  .assistant-name {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
  }

  .assistant-subtitle {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
}

.status-dot {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  display: inline-block;
}

.header-actions {
  display: flex;
  gap: 0.5rem;

  .header-btn {
    background: none;
    border: none;
    color: #6b7280;
    padding: 0.5rem;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.15s ease;

    &:hover {
      background: #f3f4f6;
      color: #374151;
    }

    i {
      font-size: 1.125rem;
    }
  }
}

// ===== BOTÕES DE SUGESTÃO RÁPIDA =====
.quick-suggestions {
  padding: 1rem;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  gap: 0.5rem;
  overflow-x: auto;

  .suggestion-btn {
    background: #f3f4f6;
    border: none;
    border-radius: 20px;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    cursor: pointer;
    transition: all 0.15s ease;
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    &:hover {
      background: #e5e7eb;
    }

    &.rapport {
      background: #fef3c7;
      color: #92400e;

      &:hover {
        background: #fde68a;
      }
    }

    &.spin {
      background: #dbeafe;
      color: #1e40af;

      &:hover {
        background: #bfdbfe;
      }
    }

    &.objection {
      background: #fce7f3;
      color: #be185d;

      &:hover {
        background: #fbcfe8;
      }
    }

    &.whatsapp {
      background: #dcfce7;
      color: #166534;

      &:hover {
        background: #bbf7d0;
      }
    }

    i {
      font-size: 1rem;
    }
  }
}

// ===== ÁREA DE CHAT =====
.chat-area {
  flex: 1;
  padding: 1rem;
  padding-bottom: 120px; // Espaço para o input fixo
  overflow-y: auto;
  background: #f9fafb;
  max-height: calc(100vh - 200px); // Altura máxima considerando header e input
}

.chat-message {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1.5rem;

  &.assistant-message {
    .message-avatar {
      width: 32px;
      height: 32px;
      background: #1f2937;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      flex-shrink: 0;

      i {
        font-size: 1rem;
      }
    }

    .message-content {
      background: white;
      border-radius: 12px;
      padding: 1rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      max-width: calc(100% - 48px);

      .message-text {
        color: #374151;
        line-height: 1.5;

        p {
          margin: 0 0 0.5rem 0;

          &:last-child {
            margin-bottom: 0;
          }
        }

        ul {
          margin: 0.5rem 0;
          padding-left: 1.5rem;

          li {
            margin-bottom: 0.25rem;
          }
        }

        strong {
          font-weight: 600;
        }
      }

      .message-time {
        font-size: 0.75rem;
        color: #9ca3af;
        margin-top: 0.5rem;
      }

      .message-actions {
        margin-top: 0.75rem;
        display: flex;
        gap: 0.5rem;

        .action-btn {
          background: #f3f4f6;
          border: none;
          border-radius: 6px;
          padding: 0.375rem 0.75rem;
          font-size: 0.75rem;
          color: #374151;
          cursor: pointer;
          transition: all 0.15s ease;
          display: flex;
          align-items: center;
          gap: 0.25rem;

          &:hover {
            background: #e5e7eb;
          }

          &.copy {
            color: #059669;

            &:hover {
              background: #d1fae5;
            }
          }

          i {
            font-size: 0.75rem;
          }
        }
      }
    }
  }
}

// ===== CARD PRINCIPAL DO LEAD =====
.lead-card {
  @extend %card-base;
  margin-bottom: 1rem;
}

.lead-content {
  padding: 1.25rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.lead-avatar {
  .avatar-circle {
    width: 50px;
    height: 50px;
    background: #6c757d;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    font-weight: 600;
    color: white;
    text-transform: uppercase;
  }
}

.lead-info {
  flex: 1;
  min-width: 0;
}

.lead-name {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
  color: #212529;
  word-wrap: break-word;
}

.lead-status {
  .status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    background: #e9ecef;
    color: #6c757d;

    &.registered {
      background: #d4edda;
      color: #155724;
    }
  }
}

.lead-score {
  .score-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    background: #ffc107;
    color: #212529;
    white-space: nowrap;
  }
}

// ===== SUGESTÕES INTELIGENTES =====
.smart-suggestions {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  .suggestions-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    color: #374151;
    font-weight: 500;

    i {
      color: #f59e0b;
    }
  }

  .suggestions-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;

    .suggestion-card {
      background: #f9fafb;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      padding: 0.75rem;
      cursor: pointer;
      transition: all 0.15s ease;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.875rem;
      color: #374151;

      &:hover {
        background: #f3f4f6;
        border-color: #d1d5db;
        transform: translateY(-1px);
      }

      i {
        font-size: 1rem;
        color: #6b7280;
      }
    }
  }
}

// ===== BOTÕES DE CATEGORIA =====
.category-buttons {
  display: flex;
  gap: 0.5rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  margin-bottom: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  .category-btn {
    background: #f3f4f6;
    border: none;
    border-radius: 6px;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    color: #374151;
    cursor: pointer;
    transition: all 0.15s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    &:hover {
      background: #e5e7eb;
    }

    &.active {
      background: #1f2937;
      color: white;
    }

    i {
      font-size: 1rem;
    }
  }
}

// ===== CARDS CONTAINER =====
.cards-container {
  padding: 1rem;
  padding-bottom: 120px; // Espaço para o input fixo
  display: flex;
  flex-direction: column;
  gap: 1rem;
  overflow-y: auto;
  max-height: calc(100vh - 80px); // Altura máxima considerando o header
}

// ===== CARD BASE =====
%card-base {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f8f9fa;

  i {
    color: #6c757d;
    font-size: 0.875rem;
  }

  span {
    font-weight: 600;
    color: #212529;
    font-size: 0.875rem;
  }
}

// ===== CARD DETALHES =====
.detail-card {
  @extend %card-base;
}

.detail-grid {
  padding: 1rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.detail-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  i {
    font-size: 0.875rem;
    color: white;
  }

  &.empresa {
    background: #007bff;
  }

  &.telefone {
    background: #17a2b8;
  }

  &.segmento {
    background: #6f42c1;
  }

  &.instagram {
    background: #e1306c;
  }
}

.detail-content {
  flex: 1;
  min-width: 0;
}

.detail-label {
  font-size: 0.625rem;
  font-weight: 600;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.25rem;
}

.detail-value {
  font-size: 0.875rem;
  font-weight: 500;
  color: #212529;
  word-wrap: break-word;
  line-height: 1.3;
}

// ===== CARD LINKS =====
.links-card {
  @extend %card-base;
}

.links-grid {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.link-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.link-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  i {
    font-size: 0.875rem;
    color: white;
  }

  &.website {
    background: #007bff;
  }

  &.instagram {
    background: #e1306c;
  }

  &.ifood {
    background: #ea1d2c;
  }

  &.whatsapp {
    background: #25d366;
  }

  &.localizacao {
    background: #4285f4;
  }

  &.cardapio {
    background: #ff6b35;
  }

  &.reservas {
    background: #6f42c1;
  }

  &.concorrente {
    background: #6c757d;
  }

  &.default {
    background: #17a2b8;
  }
}

.link-content {
  flex: 1;
  min-width: 0;
}

.link-label {
  font-size: 0.625rem;
  font-weight: 600;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.25rem;
}

.link-value {
  font-size: 0.875rem;
  font-weight: 500;
  color: #007bff;
  text-decoration: none;
  word-wrap: break-word;
  line-height: 1.3;

  &:hover {
    text-decoration: underline;
  }
}

// ===== CARD MÉTRICAS =====
.metrics-card {
  @extend %card-base;
}

.metrics-row {
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  gap: 0.5rem;
}

.metric-circle {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  flex: 1;

  i {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
    margin-bottom: 0.5rem;
  }

  &.fase i {
    background: #dc3545;
  }

  &.resposta i {
    background: #28a745;
  }

  &.potencial i {
    background: #ffc107;
    color: #212529;
  }

  &.prioridade i {
    background: #fd7e14;
  }
}

.metric-info {
  text-align: center;
}

.metric-label {
  font-size: 0.625rem;
  font-weight: 600;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.125rem;
}

.metric-value {
  font-size: 0.75rem;
  font-weight: 600;
  color: #212529;
}

// ===== CARD STATUS CRM =====
.status-crm-card {
  @extend %card-base;

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;

    .header-left {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      i {
        color: #6c757d;
        font-size: 0.875rem;
      }

      span {
        font-weight: 600;
        color: #212529;
        font-size: 0.875rem;
      }
    }

    .header-right {
      .bitrix-btn {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        background: #007bff;
        color: white;
        text-decoration: none;
        border-radius: 6px;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.15s ease;

        &:hover {
          background: #0056b3;
          text-decoration: none;
          color: white;
        }

        i {
          font-size: 0.875rem;
          color: white;
        }
      }
    }
  }
}

.status-crm-content {
  padding: 1rem;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #28a745;
  flex-shrink: 0;

  &.offline {
    background: #6c757d;
  }
}

.status-text {
  font-size: 0.875rem;
  color: #212529;
  line-height: 1.4;
}

// ===== CARD MODO =====
.mode-card {
  @extend %card-base;
}

.mode-options {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

// Botão de ação no modo - complementa as classes Bootstrap
.mode-action-btn {
  &.rapport-btn {
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.15s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
    }

    &:active {
      transform: translateY(0);
    }

    &:focus {
      box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }

    i {
      font-size: 0.875rem;
    }
  }
}

.mode-checkbox {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  user-select: none;

  input[type="checkbox"] {
    width: 18px;
    height: 18px;
    margin: 0;
    accent-color: #007bff;
  }

  span:not(.checkmark) {
    font-size: 0.875rem;
    color: #212529;
  }
}

// ===== CARD SPIN =====
.spin-card {
  @extend %card-base;
}

// ===== TIMELINE HORIZONTAL SPIN =====
.spin-timeline {
  padding: 3rem 1rem 2rem 1rem;
}

.timeline-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0;
  max-width: 100%;
  overflow-x: auto;
}

.timeline-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 80px;

  &:hover .step-circle {
    transform: scale(1.1);
  }

  &.auto-step {
    position: absolute;
    top: 1rem;
    right: 1rem;
    min-width: auto;

    .step-circle {
      width: 36px;
      height: 36px;
      background: #6c757d;

      &:hover {
        background: #5a6268;
      }
    }

    .step-label {
      font-size: 0.75rem;
      margin-top: 0.25rem;
    }
  }
}

.step-circle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  border: 3px solid transparent;

  // Estado padrão (futuro)
  background: #f8f9fa;
  color: #6c757d;
  border-color: #dee2e6;

  // Estado ativo (atual)
  .timeline-step.active & {
    background: #6f42c1;
    color: white;
    border-color: #6f42c1;
    box-shadow: 0 0 0 4px rgba(111, 66, 193, 0.2);
  }

  // Estado completado
  .timeline-step.completed & {
    background: #28a745;
    color: white;
    border-color: #28a745;
  }

  i {
    font-size: 1.125rem;
  }

  span {
    font-size: 1rem;
    font-weight: 700;
  }
}

.timeline-line {
  width: 60px;
  height: 4px;
  background: #dee2e6;
  transition: all 0.3s ease;

  &.completed {
    background: #28a745;
  }
}

.step-label {
  margin-top: 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6c757d;
  text-align: center;

  .timeline-step.active & {
    color: #6f42c1;
    font-weight: 600;
  }

  .timeline-step.completed & {
    color: #28a745;
    font-weight: 600;
  }
}

// Responsividade
@media (max-width: 480px) {
  .timeline-container {
    gap: 0;
  }

  .timeline-line {
    width: 40px;
  }

  .step-circle {
    width: 40px;
    height: 40px;
    font-size: 0.875rem;
  }

  .step-label {
    font-size: 0.75rem;
  }
}

.tom-section {
  padding: 0 1rem 1rem 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.tom-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #212529;
  white-space: nowrap;
}

.tom-select {
  flex: 1;
  padding: 0.375rem 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  color: #495057;

  &:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }
}

// ===== BOTÃO PRINCIPAL =====
.main-action {
  padding: 0 1rem 1rem 1rem;
  margin-top: 1rem;
}

.generate-btn {
  width: 100%;
  padding: 0.875rem 1.5rem;
  background: #6f42c1;
  color: white;
  border: none;
  border-radius: 25px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.15s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;

  &:hover:not(:disabled) {
    background: #5a359a;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(111, 66, 193, 0.3);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  i {
    font-size: 0.875rem;
  }
}

// ===== CARD SUGESTÕES =====
.suggestions-card {
  @extend %card-base;
  margin-top: 1rem;
}

.suggestions-badges {
  display: flex;
  gap: 0.5rem;
  margin-left: auto;
}

.suggestion-count, .suggestion-confidence {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.625rem;
  font-weight: 500;
}

.suggestion-count {
  background: #e9ecef;
  color: #495057;
}

.suggestion-confidence {
  background: #d4edda;
  color: #155724;
}

.suggestion-content {
  padding: 1rem;
}

.suggestion-textarea {
  width: 100%;
  border: 1px solid #ced4da;
  border-radius: 6px;
  padding: 0.75rem;
  font-size: 0.875rem;
  font-family: inherit;
  resize: vertical;
  margin-bottom: 1rem;

  &:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }
}

.suggestion-actions {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.action-btn {
  flex: 1;
  padding: 0.5rem 1rem;
  border: 1px solid #ced4da;
  border-radius: 6px;
  background: white;
  color: #495057;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;

  &:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
  }

  &.copy:hover {
    background: #d4edda;
    border-color: #28a745;
    color: #155724;
  }

  &.edit:hover {
    background: #cce5ff;
    border-color: #007bff;
    color: #004085;
  }

  &.refresh:hover {
    background: #fff3cd;
    border-color: #ffc107;
    color: #856404;
  }

  i {
    font-size: 0.75rem;
  }
}

.suggestion-nav {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e9ecef;
}

.nav-btn {
  background: #f8f9fa;
  border: 1px solid #ced4da;
  border-radius: 6px;
  padding: 0.375rem;
  color: #495057;
  cursor: pointer;
  transition: all 0.15s ease;

  &:hover:not(:disabled) {
    background: #e9ecef;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.nav-text {
  font-size: 0.75rem;
  font-weight: 500;
  color: #495057;
}

// ===== CAPTURA RÁPIDA =====
.capture-card {
  @extend %card-base;
  margin-top: 1rem;
}

.capture-form {
  padding: 1rem;
}

.form-group {
  margin-bottom: 1rem;

  label {
    display: block;
    font-size: 0.75rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.25rem;
  }
}

.form-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 0.875rem;
  font-family: inherit;

  &:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }

  &[readonly] {
    background: #f8f9fa;
    color: #6c757d;
  }

  &::placeholder {
    color: #adb5bd;
  }
}

.save-btn {
  width: 100%;
  padding: 0.75rem 1.5rem;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;

  &:hover {
    background: #218838;
  }
}

// ===== ERROR ALERT =====
.error-alert {
  margin: 1rem;
  padding: 0.75rem 1rem;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 6px;
  color: #721c24;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  i {
    font-size: 0.875rem;
  }

  span {
    font-size: 0.875rem;
  }
}

// ===== SEQUÊNCIA DE MENSAGENS =====
.message-sequence {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.message-item {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 0.75rem;
  background: #f8f9fa;
  transition: all 0.15s ease;

  &:hover {
    border-color: #ced4da;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.message-number {
  font-size: 0.75rem;
  font-weight: 600;
  color: #495057;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.copy-individual {
  padding: 0.25rem 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background: white;
  color: #495057;
  font-size: 0.625rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;

  &:hover {
    background: #d4edda;
    border-color: #28a745;
    color: #155724;
  }

  i {
    font-size: 0.625rem;
  }
}

.message-content {
  margin-top: 0.5rem;
}

.message-textarea {
  width: 100%;
  border: 1px solid #ced4da;
  border-radius: 6px;
  padding: 0.75rem;
  font-size: 0.875rem;
  font-family: inherit;
  resize: none;
  background: white;
  line-height: 1.4;

  &:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }
}

.sequence-actions {
  display: flex;
  justify-content: center;
  padding-top: 0.5rem;
  border-top: 1px solid #e9ecef;
  margin-top: 0.5rem;
}

// ===== UTILITÁRIOS =====
@keyframes rotating {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.rotating {
  animation: rotating 1s linear infinite;
}

.mr-1 { margin-right: 0.25rem !important; }

.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

// ===== ÁREA DE INPUT DO CHAT =====
.chat-input-area {
  background: white;
  border-top: 1px solid #e5e7eb;
  padding: 1rem;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);

  .input-container {
    display: flex;
    gap: 0.75rem;
    align-items: flex-end;

    .chat-input {
      flex: 1;
      border: 1px solid #d1d5db;
      border-radius: 20px;
      padding: 0.75rem 1rem;
      font-size: 0.875rem;
      outline: none;
      transition: all 0.15s ease;

      &:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }

      &:disabled {
        background: #f9fafb;
        color: #9ca3af;
        cursor: not-allowed;
      }

      &::placeholder {
        color: #9ca3af;
      }
    }

    .send-btn {
      width: 40px;
      height: 40px;
      background: #6b7280;
      border: none;
      border-radius: 50%;
      color: white;
      cursor: pointer;
      transition: all 0.15s ease;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover:not(:disabled) {
        background: #374151;
        transform: scale(1.05);
      }

      &:disabled {
        background: #d1d5db;
        cursor: not-allowed;
        transform: none;
      }

      i {
        font-size: 1rem;
      }
    }
  }

  .input-hint {
    font-size: 0.75rem;
    color: #9ca3af;
    text-align: center;
    margin-top: 0.5rem;
  }
}

// ===== RESPONSIVIDADE =====
@media (max-width: 768px) {
  .cards-container {
    padding: 0.75rem;
    padding-bottom: 140px; // Mais espaço em mobile
    max-height: calc(100vh - 60px);
  }

  .chat-input-area {
    padding: 0.75rem;

    .input-hint {
      font-size: 0.625rem;
    }
  }

  .chat-area {
    padding: 0.75rem;
    padding-bottom: 140px;
    max-height: calc(100vh - 180px);
  }
}
